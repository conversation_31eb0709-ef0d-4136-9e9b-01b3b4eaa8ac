using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern sọc
    /// </summary>
    public class StripesFilter : IFilter
    {
        private readonly int stripeWidth;
        private readonly int gapWidth;
        private readonly float rotation;
        private readonly bool fillStripes;
        private readonly int offsetX;
        private readonly int offsetY;

        public StripesFilter(int stripeWidth = 3, int gapWidth = 3, float rotation = 0f, 
            bool fillStripes = true, int offsetX = 0, int offsetY = 0)
        {
            this.stripeWidth = Mathf.Max(1, stripeWidth);
            this.gapWidth = Mathf.Max(1, gapWidth);
            this.rotation = rotation;
            this.fillStripes = fillStripes;
            this.offsetX = offsetX;
            this.offsetY = offsetY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool isOnStripe = IsOnStripe(i, j, rows, cols);

                    if ((fillStripes && isOnStripe) || (!fillStripes && !isOnStripe))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private bool IsOnStripe(int row, int col, int totalRows, int totalCols)
        {
            // Transform point based on rotation
            Vector2 transformedPoint = TransformPoint(row, col, totalRows, totalCols);
            
            // Apply offset
            int adjustedCoord = Mathf.RoundToInt(transformedPoint.x) + offsetX;
            
            // Calculate stripe pattern
            int cycleLength = stripeWidth + gapWidth;
            int positionInCycle = ((adjustedCoord % cycleLength) + cycleLength) % cycleLength; // Handle negative values
            
            return positionInCycle < stripeWidth;
        }

        private Vector2 TransformPoint(int row, int col, int totalRows, int totalCols)
        {
            // Center the coordinate system
            float centerRow = (totalRows - 1) * 0.5f;
            float centerCol = (totalCols - 1) * 0.5f;
            
            float x = col - centerCol;
            float y = row - centerRow;

            // Apply rotation
            float rotRad = rotation * Mathf.Deg2Rad;
            float rotatedX = x * Mathf.Cos(rotRad) - y * Mathf.Sin(rotRad);
            float rotatedY = x * Mathf.Sin(rotRad) + y * Mathf.Cos(rotRad);

            return new Vector2(rotatedX, rotatedY);
        }
    }
}
