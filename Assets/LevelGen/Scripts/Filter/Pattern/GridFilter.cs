using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern lưới
    /// </summary>
    public class GridFilter : IFilter
    {
        private readonly int cellSize;
        private readonly float lineThickness;
        private readonly bool fillLines;
        private readonly int offsetX;
        private readonly int offsetY;

        public GridFilter(int cellSize = 5, float lineThickness = 1f, bool fillLines = true, int offsetX = 0, int offsetY = 0)
        {
            this.cellSize = Mathf.Max(1, cellSize);
            this.lineThickness = Mathf.Max(0.5f, lineThickness);
            this.fillLines = fillLines;
            this.offsetX = offsetX;
            this.offsetY = offsetY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    int adjustedI = i + offsetY;
                    int adjustedJ = j + offsetX;
                    
                    bool isOnGridLine = IsOnGridLine(adjustedI, adjustedJ);

                    if ((fillLines && isOnGridLine) || (!fillLines && !isOnGridLine))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private bool IsOnGridLine(int row, int col)
        {
            // Check if point is on horizontal grid line
            bool onHorizontalLine = (row % cellSize) < lineThickness;
            
            // Check if point is on vertical grid line
            bool onVerticalLine = (col % cellSize) < lineThickness;
            
            return onHorizontalLine || onVerticalLine;
        }
    }
}
