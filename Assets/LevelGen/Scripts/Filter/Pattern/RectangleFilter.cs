using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình chữ nhật hoặc vuông
    /// </summary>
    public class RectangleFilter : IFilter
    {
        private readonly float widthScale;
        private readonly float heightScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly bool roundedCorners;
        private readonly float cornerRadiusScale;

        public RectangleFilter(float widthScale = 0.8f, float heightScale = 0.8f, bool fillInside = true,
            float centerX = 0.5f, float centerY = 0.5f, bool roundedCorners = false, float cornerRadiusScale = 0.1f)
        {
            this.widthScale = widthScale;
            this.heightScale = heightScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.roundedCorners = roundedCorners;
            this.cornerRadiusScale = cornerRadiusScale;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;

            // Tính kích thước dựa trên matrix size và scale
            float minSize = Mathf.Min(rows, cols);
            float halfWidth = minSize * widthScale * 0.5f;
            float halfHeight = minSize * heightScale * 0.5f;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool isInside;

                    if (roundedCorners)
                    {
                        isInside = IsInsideRoundedRectangle(i, j, centerRow, centerCol, halfWidth, halfHeight, cornerRadiusScale, minSize);
                    }
                    else
                    {
                        isInside = Mathf.Abs(i - centerRow) <= halfHeight && Mathf.Abs(j - centerCol) <= halfWidth;
                    }

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private bool IsInsideRoundedRectangle(int row, int col, float centerRow, float centerCol,
            float halfWidth, float halfHeight, float cornerRadiusScale, float minSize)
        {
            float x = col - centerCol;
            float y = row - centerRow;

            // Tính corner radius dựa trên scale và minSize
            float cornerRadius = cornerRadiusScale * minSize;

            // Kiểm tra nếu điểm nằm trong phần chữ nhật chính
            if (Mathf.Abs(x) <= halfWidth - cornerRadius || Mathf.Abs(y) <= halfHeight - cornerRadius)
            {
                return Mathf.Abs(x) <= halfWidth && Mathf.Abs(y) <= halfHeight;
            }

            // Kiểm tra các góc bo tròn
            float cornerX = halfWidth - cornerRadius;
            float cornerY = halfHeight - cornerRadius;

            if (Mathf.Abs(x) > cornerX && Mathf.Abs(y) > cornerY)
            {
                float dx = Mathf.Abs(x) - cornerX;
                float dy = Mathf.Abs(y) - cornerY;
                return dx * dx + dy * dy <= cornerRadius * cornerRadius;
            }

            return Mathf.Abs(x) <= halfWidth && Mathf.Abs(y) <= halfHeight;
        }
    }
}
