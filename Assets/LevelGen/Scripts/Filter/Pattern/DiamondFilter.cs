using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình kim c<PERSON>ng
    /// </summary>
    public class DiamondFilter : IFilter
    {
        private readonly float sizeScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;

        public DiamondFilter(float sizeScale = 0.8f, bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f)
        {
            this.sizeScale = sizeScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;

            // Tính maxDistance dựa trên kích thước matrix và scale, đảm bảo minimum size
            float minSize = Mathf.Min(rows, cols);
            float maxDistance = Mathf.Max(2f, minSize * sizeScale * 0.5f);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    // Tính khoảng cách Manhattan (diamond shape)
                    float distance = Mathf.Abs(i - centerRow) + Mathf.Abs(j - centerCol);
                    bool isInside = distance <= maxDistance;

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
