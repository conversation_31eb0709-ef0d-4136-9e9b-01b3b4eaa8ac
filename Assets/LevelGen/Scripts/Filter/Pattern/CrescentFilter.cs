using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình lưỡi liềm (crescent)
    /// </summary>
    public class CrescentFilter : IFilter
    {
        private readonly float outerRadiusScale;
        private readonly float innerRadiusScale;
        private readonly float offsetScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public CrescentFilter(float outerRadiusScale = 0.4f, float innerRadiusScale = 0.3f, float offsetScale = 0.15f,
            bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.outerRadiusScale = outerRadiusScale;
            this.innerRadiusScale = innerRadiusScale;
            this.offsetScale = offsetScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;
            float minSize = Mathf.Min(rows, cols);
            
            float outerRadius = minSize * outerRadiusScale * 0.5f;
            float innerRadius = minSize * innerRadiusScale * 0.5f;
            float offset = minSize * offsetScale * 0.5f;

            // Calculate inner circle center with offset
            float rotRad = rotation * Mathf.Deg2Rad;
            float innerCenterCol = centerCol + offset * Mathf.Cos(rotRad);
            float innerCenterRow = centerRow + offset * Mathf.Sin(rotRad);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float x = j - centerCol;
                    float y = i - centerRow;
                    float outerDistance = Mathf.Sqrt(x * x + y * y);

                    float innerX = j - innerCenterCol;
                    float innerY = i - innerCenterRow;
                    float innerDistance = Mathf.Sqrt(innerX * innerX + innerY * innerY);

                    // Point is inside crescent if it's inside outer circle but outside inner circle
                    bool isInside = outerDistance <= outerRadius && innerDistance > innerRadius;

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
