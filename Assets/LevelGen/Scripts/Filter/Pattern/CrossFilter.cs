using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter
{
    /// <summary>
    /// Filter tạo hình thập tự hoặc dấu cộng
    /// </summary>
    public class CrossFilter : IFilter
    {
        private readonly float horizontalWidth;
        private readonly float verticalWidth;
        private readonly float horizontalLength;
        private readonly float verticalLength;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public CrossFilter(float horizontalWidthScale = 0.1f, float verticalWidthScale = 0.1f,
            float horizontalLengthScale = 0.8f, float verticalLengthScale = 0.8f,
            bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.horizontalWidth = horizontalWidthScale;
            this.verticalWidth = verticalWidthScale;
            this.horizontalLength = horizontalLengthScale;
            this.verticalLength = verticalLengthScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float x = j - centerCol;
                    float y = i - centerRow;

                    // Apply rotation if needed
                    if (rotation != 0)
                    {
                        float cos = Mathf.Cos(rotation * Mathf.Deg2Rad);
                        float sin = Mathf.Sin(rotation * Mathf.Deg2Rad);
                        float newX = x * cos - y * sin;
                        float newY = x * sin + y * cos;
                        x = newX;
                        y = newY;
                    }

                    bool isInside = false;

                    // Tính kích thước dựa trên matrix size và scale
                    float minSize = Mathf.Min(rows, cols);

                    // Check horizontal bar
                    float halfHorizontalLength = minSize * horizontalLength * 0.5f;
                    float halfHorizontalWidth = minSize * horizontalWidth * 0.5f;

                    if (Mathf.Abs(x) <= halfHorizontalLength && Mathf.Abs(y) <= halfHorizontalWidth)
                    {
                        isInside = true;
                    }

                    // Check vertical bar
                    float halfVerticalLength = minSize * verticalLength * 0.5f;
                    float halfVerticalWidth = minSize * verticalWidth * 0.5f;
                    
                    if (Mathf.Abs(y) <= halfVerticalLength && Mathf.Abs(x) <= halfVerticalWidth)
                    {
                        isInside = true;
                    }

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
