using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo pattern sóng
    /// </summary>
    public class WaveFilter : IFilter
    {
        private readonly float frequency;
        private readonly float amplitude;
        private readonly float thickness;
        private readonly WaveDirection direction;
        private readonly float phase;
        private readonly WaveType waveType;

        public enum WaveDirection
        {
            Horizontal,
            Vertical,
            Diagonal,
            Radial
        }

        public enum WaveType
        {
            Sine,
            Cosine,
            Square,
            Triangle
        }

        public WaveFilter(float frequency = 2f, float amplitudeScale = 0.1f, float thicknessScale = 0.05f,
            WaveDirection direction = WaveDirection.Horizontal, float phase = 0f, WaveType waveType = WaveType.Sine)
        {
            this.frequency = frequency;
            this.amplitude = amplitudeScale;
            this.thickness = thicknessScale;
            this.direction = direction;
            this.phase = phase;
            this.waveType = waveType;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            // T<PERSON>h kích thước dựa trên matrix size và scale
            float minSize = Mathf.Min(rows, cols);
            float thicknessPixels = minSize * thickness;
            float amplitudePixels = minSize * amplitude;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    bool isOnWave = false;

                    switch (direction)
                    {
                        case WaveDirection.Horizontal:
                            isOnWave = IsOnHorizontalWave(i, j, rows, cols, amplitudePixels, thicknessPixels);
                            break;
                        case WaveDirection.Vertical:
                            isOnWave = IsOnVerticalWave(i, j, rows, cols, amplitudePixels, thicknessPixels);
                            break;
                        case WaveDirection.Diagonal:
                            isOnWave = IsOnDiagonalWave(i, j, rows, cols, amplitudePixels, thicknessPixels);
                            break;
                        case WaveDirection.Radial:
                            isOnWave = IsOnRadialWave(i, j, rows, cols, amplitudePixels, thicknessPixels);
                            break;
                    }

                    if (isOnWave)
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private bool IsOnHorizontalWave(int i, int j, int rows, int cols, float amplitudePixels, float thicknessPixels)
        {
            float normalizedX = (float)j / cols;
            float waveY = rows * 0.5f + amplitudePixels * GetWaveValue(normalizedX * frequency + phase);
            return Mathf.Abs(i - waveY) <= thicknessPixels;
        }

        private bool IsOnVerticalWave(int i, int j, int rows, int cols, float amplitudePixels, float thicknessPixels)
        {
            float normalizedY = (float)i / rows;
            float waveX = cols * 0.5f + amplitudePixels * GetWaveValue(normalizedY * frequency + phase);
            return Mathf.Abs(j - waveX) <= thicknessPixels;
        }

        private bool IsOnDiagonalWave(int i, int j, int rows, int cols, float amplitudePixels, float thicknessPixels)
        {
            float normalizedDiag = (float)(i + j) / (rows + cols);
            float perpDistance = (j - i) * 0.5f;
            float waveOffset = amplitudePixels * GetWaveValue(normalizedDiag * frequency + phase);
            return Mathf.Abs(perpDistance - waveOffset) <= thicknessPixels;
        }

        private bool IsOnRadialWave(int i, int j, int rows, int cols, float amplitudePixels, float thicknessPixels)
        {
            float centerX = (cols - 1) * 0.5f;
            float centerY = (rows - 1) * 0.5f;
            float distance = Mathf.Sqrt((j - centerX) * (j - centerX) + (i - centerY) * (i - centerY));
            float normalizedDistance = distance / (Mathf.Min(rows, cols) * 0.5f);
            float waveRadius = distance + amplitudePixels * GetWaveValue(normalizedDistance * frequency + phase);
            return Mathf.Abs(distance - waveRadius + amplitudePixels) <= thicknessPixels;
        }

        private float GetWaveValue(float input)
        {
            switch (waveType)
            {
                case WaveType.Sine:
                    return Mathf.Sin(input * 2 * Mathf.PI);
                case WaveType.Cosine:
                    return Mathf.Cos(input * 2 * Mathf.PI);
                case WaveType.Square:
                    return Mathf.Sign(Mathf.Sin(input * 2 * Mathf.PI));
                case WaveType.Triangle:
                    float t = (input % 1.0f);
                    return t < 0.5f ? (4 * t - 1) : (3 - 4 * t);
                default:
                    return Mathf.Sin(input * 2 * Mathf.PI);
            }
        }
    }
}
