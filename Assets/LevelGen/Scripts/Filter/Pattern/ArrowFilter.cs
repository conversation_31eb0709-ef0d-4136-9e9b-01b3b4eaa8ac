using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình mũi tên
    /// </summary>
    public class ArrowFilter : IFilter
    {
        private readonly float lengthScale;
        private readonly float headWidthScale;
        private readonly float shaftWidthScale;
        private readonly float headLengthScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public ArrowFilter(float lengthScale = 0.7f, float headWidthScale = 0.4f, float shaftWidthScale = 0.15f, 
            float headLengthScale = 0.3f, bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.lengthScale = lengthScale;
            this.headWidthScale = headWidthScale;
            this.shaftWidthScale = shaftWidthScale;
            this.headLengthScale = headLengthScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;
            float minSize = Mathf.Min(rows, cols);

            // Calculate arrow dimensions
            float totalLength = minSize * lengthScale * 0.5f;
            float headWidth = minSize * headWidthScale * 0.5f;
            float shaftWidth = minSize * shaftWidthScale * 0.5f;
            float headLength = totalLength * headLengthScale;
            float shaftLength = totalLength - headLength;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    // Transform point to arrow coordinate system
                    Vector2 point = TransformPoint(j, i, centerCol, centerRow, rotation);
                    bool isInside = IsInsideArrow(point, totalLength, headWidth, shaftWidth, headLength, shaftLength);

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private Vector2 TransformPoint(float x, float y, float centerX, float centerY, float rotation)
        {
            // Translate to origin
            float translatedX = x - centerX;
            float translatedY = y - centerY;

            // Rotate
            float rotRad = -rotation * Mathf.Deg2Rad; // Negative for counter-clockwise
            float rotatedX = translatedX * Mathf.Cos(rotRad) - translatedY * Mathf.Sin(rotRad);
            float rotatedY = translatedX * Mathf.Sin(rotRad) + translatedY * Mathf.Cos(rotRad);

            return new Vector2(rotatedX, rotatedY);
        }

        private bool IsInsideArrow(Vector2 point, float totalLength, float headWidth, float shaftWidth, float headLength, float shaftLength)
        {
            float x = point.x;
            float y = point.y;

            // Arrow points to the right (positive X direction)
            float halfTotalLength = totalLength * 0.5f;
            
            // Check if point is within arrow bounds
            if (x < -halfTotalLength || x > halfTotalLength)
                return false;

            // Arrow head (right side)
            if (x > halfTotalLength - headLength)
            {
                // Calculate width at this x position (triangular head)
                float distanceFromTip = halfTotalLength - x;
                float widthAtX = (distanceFromTip / headLength) * headWidth;
                return Mathf.Abs(y) <= widthAtX;
            }
            // Arrow shaft (left side)
            else
            {
                return Mathf.Abs(y) <= shaftWidth * 0.5f;
            }
        }
    }
}
