using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình vòng tròn (ring/donut)
    /// </summary>
    public class RingFilter : IFilter
    {
        private readonly float outerRadiusScale;
        private readonly float innerRadiusScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;

        public RingFilter(float outerRadiusScale = 0.4f, float innerRadiusScale = 0.2f, 
            bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f)
        {
            this.outerRadiusScale = outerRadiusScale;
            this.innerRadiusScale = innerRadiusScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;
            float minSize = Mathf.Min(rows, cols);
            
            float outerRadius = minSize * outerRadiusScale * 0.5f;
            float innerRadius = minSize * innerRadiusScale * 0.5f;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    float x = j - centerCol;
                    float y = i - centerRow;
                    float distance = Mathf.Sqrt(x * x + y * y);

                    // Point is inside ring if it's between inner and outer radius
                    bool isInside = distance <= outerRadius && distance >= innerRadius;

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }
    }
}
