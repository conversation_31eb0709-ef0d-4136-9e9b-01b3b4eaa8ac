using Sirenix.OdinInspector;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Common
{
    /// <summary>
    /// Base ScriptableObject cho tất cả filter config
    /// </summary>
    public abstract class FilterConfigBase : ScriptableObject
    {
        [Header("Basic Info")]
        [SerializeField]
        [ReadOnly]
        private string filterName = "New Filter";

        public abstract string GetFilterName();

        /// <summary>
        /// Set filter name - useful when creating instances
        /// </summary>
        public void SetFilterName(string name)
        {
            filterName = name;
        }
        
        public void SetFilterName()
        {
            filterName = GetFilterName();
        }

        /// <summary>
        /// Tạo filter với parameters hiện tại
        /// </summary>
        public abstract IFilter CreateFilter();

        /// <summary>
        /// Tạo filter với parameters ngẫu nhiên
        /// </summary>
        public virtual IFilter CreateRandomFilter()
        {
            var filter = CreateRandomFilterInternal();
            return filter;
        }

        /// <summary>
        /// Override này để implement random logic cho từng loại filter
        /// </summary>
        protected abstract IFilter CreateRandomFilterInternal();

        /// <summary>
        /// Utility method để random bool với probability
        /// </summary>
        protected bool RandomBool(float trueChance = 0.5f)
        {
            return UnityEngine.Random.value < trueChance;
        }

        /// <summary>
        /// Utility method để random center position
        /// </summary>
        protected Vector2 RandomCenter()
        {
            return Vector2.one * 0.5f;
            
            return new Vector2(
                UnityEngine.Random.Range(0.2f, 0.8f),
                UnityEngine.Random.Range(0.2f, 0.8f)
            );
        }

        /// <summary>
        /// Utility method để random scale
        /// </summary>
        protected float RandomScale(float min = 0.3f, float max = 1.2f)
        {
            return UnityEngine.Random.Range(min, max);
        }

        /// <summary>
        /// Utility method để random rotation
        /// </summary>
        protected float RandomRotation()
        {
            return UnityEngine.Random.Range(0f, 360f);
        }
    }
}
