using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter
{
    /// <summary>
    /// Group để quản lý và tạo các Advanced Shape Filter Configs
    /// </summary>
    [CreateAssetMenu(fileName = "AdvancedShapesConfigGroup", menuName = "LevelGen/Config Groups/Advanced Shapes Group")]
    public class AdvancedShapesConfigGroup : ScriptableObject
    {
        [Header("Advanced Shapes Config Group")]
        [InfoBox("Use buttons below to create individual advanced shape filter configs")]
        
        [<PERSON><PERSON>("Create Triangle Filter Config", ButtonSizes.Large)]
        public void CreateTriangleConfig()
        {
            CreateConfig<TriangleFilterConfig>("TriangleFilter");
        }
        
        [But<PERSON>("Create Crescent Filter Config", ButtonSizes.Large)]
        public void CreateCrescentConfig()
        {
            CreateConfig<CrescentFilterConfig>("CrescentFilter");
        }
        
        [But<PERSON>("Create Ring Filter Config", ButtonSizes.Large)]
        public void CreateRingConfig()
        {
            CreateConfig<RingFilterConfig>("RingFilter");
        }
        
        [<PERSON><PERSON>("Create Arrow Filter Config", ButtonSizes.Large)]
        public void CreateArrowConfig()
        {
            CreateConfig<ArrowFilterConfig>("ArrowFilter");
        }
        
        [Button("Create Lightning Filter Config", ButtonSizes.Large)]
        public void CreateLightningConfig()
        {
            CreateConfig<LightningFilterConfig>("LightningFilter");
        }
        
        [Button("Create Butterfly Filter Config", ButtonSizes.Large)]
        public void CreateButterflyConfig()
        {
            CreateConfig<ButterflyFilterConfig>("ButterflyFilter");
        }
        
        [Space(20)]
        [Button("Create All Advanced Shape Configs", ButtonSizes.Large)]
        public void CreateAllAdvancedShapeConfigs()
        {
            CreateTriangleConfig();
            CreateCrescentConfig();
            CreateRingConfig();
            CreateArrowConfig();
            CreateLightningConfig();
            CreateButterflyConfig();
            
            Debug.Log("Created all Advanced Shape filter configs!");
        }
        
        private void CreateConfig<T>(string fileName) where T : FilterConfig
        {
#if UNITY_EDITOR
            var config = ScriptableObject.CreateInstance<T>();
            config.filterName = fileName;
            
            string path = $"Assets/LevelGen/Scripts/Filter/Configs/AdvancedShapes/{fileName}.asset";
            UnityEditor.AssetDatabase.CreateAsset(config, path);
            UnityEditor.AssetDatabase.SaveAssets();
            
            Debug.Log($"Created {typeof(T).Name} at {path}");
            
            // Ping the created asset
            UnityEditor.EditorGUIUtility.PingObject(config);
#endif
        }
    }
}
