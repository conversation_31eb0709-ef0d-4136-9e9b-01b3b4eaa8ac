using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Spiral Filter
    /// </summary>
    [CreateAssetMenu(fileName = "SpiralFilter", menuName = "LevelGen/Filters/Spiral Filter")]
    public class SpiralFilterConfigBase : FilterConfigBase
    {
        [Header("Spiral Parameters")]
        [Range(0.05f, 0.5f)] public float spiralTightness = 0.1f;
        [Range(0.01f, 0.2f)] public float thicknessScale = 0.05f;
        public bool clockwise = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(1, 10)] public int turns = 3;

        public override string GetFilterName()
        {
            return "Spiral Filter";
        }

        public override IFilter CreateFilter()
        {
            return new SpiralFilter(spiralTightness, thicknessScale, clockwise, centerX, centerY, turns);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new SpiralFilter(
                UnityEngine.Random.Range(0.05f, 0.3f), // spiralTightness
                UnityEngine.Random.Range(0.02f, 0.1f), // thicknessScale
                RandomBool(), // clockwise
                RandomCenter().x, // centerX
                RandomCenter().y, // centerY
                UnityEngine.Random.Range(2, 6) // turns
            );
        }
    }
}
