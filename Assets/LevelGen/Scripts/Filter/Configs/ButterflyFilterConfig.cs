using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Butterfly Filter
    /// </summary>
    [CreateAssetMenu(fileName = "ButterflyFilter", menuName = "LevelGen/Filters/Butterfly Filter")]
    public class ButterflyFilterConfig : FilterConfigBase
    {
        [Header("Butterfly Parameters")]
        [Range(0.3f, 1.5f)] public float wingSpanScale = 0.8f;
        [Range(0.2f, 1f)] public float wingHeightScale = 0.6f;
        [Range(0.02f, 0.2f)] public float bodyWidthScale = 0.08f;
        [Range(0.3f, 1.2f)] public float bodyLengthScale = 0.7f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Butterfly Filter";
        }

        public override IFilter CreateFilter()
        {
            return new ButterflyFilter(wingSpanScale, wingHeightScale, bodyWidthScale, bodyLengthScale, 
                fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new ButterflyFilter(
                UnityEngine.Random.Range(0.5f, 1.2f), // wingSpanScale
                UnityEngine.Random.Range(0.4f, 0.8f), // wingHeightScale
                UnityEngine.Random.Range(0.04f, 0.15f), // bodyWidthScale
                UnityEngine.Random.Range(0.5f, 1f), // bodyLengthScale
                RandomBool(0.8f), // fillInside (80% chance)
                RandomCenter().x, // centerX
                RandomCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
