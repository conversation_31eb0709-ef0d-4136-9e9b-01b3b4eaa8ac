using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Noise Filter
    /// </summary>
    [CreateAssetMenu(fileName = "NoiseFilter", menuName = "LevelGen/Filters/Noise Filter")]
    public class NoiseFilterConfigBase : FilterConfigBase
    {
        [Header("Noise Parameters")]
        [Range(0.01f, 0.5f)] public float scale = 0.1f;
        [Range(0f, 1f)] public float threshold = 0.5f;
        public bool fillAboveThreshold = true;
        [Range(1, 6)] public int octaves = 1;
        [Range(0.1f, 1f)] public float persistence = 0.5f;
        [Range(1f, 4f)] public float lacunarity = 2f;
        public Vector2 offset = Vector2.zero;
        public int seed = 0;

        public override string GetFilterName()
        {
            return "Noise Filter";
        }

        public override IFilter CreateFilter()
        {
            return new NoiseFilter(scale, threshold, fillAboveThreshold, octaves, persistence, lacunarity, offset, seed);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new NoiseFilter(
                UnityEngine.Random.Range(0.05f, 0.3f), // scale
                UnityEngine.Random.Range(0.3f, 0.7f), // threshold
                RandomBool(), // fillAboveThreshold
                UnityEngine.Random.Range(1, 4), // octaves
                UnityEngine.Random.Range(0.3f, 0.8f), // persistence
                UnityEngine.Random.Range(1.5f, 3f), // lacunarity
                new Vector2(UnityEngine.Random.Range(-100f, 100f), UnityEngine.Random.Range(-100f, 100f)), // offset
                UnityEngine.Random.Range(1, 1000) // seed
            );
        }
    }
}
