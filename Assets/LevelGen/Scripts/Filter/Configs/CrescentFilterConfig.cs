using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Crescent Filter
    /// </summary>
    [CreateAssetMenu(fileName = "CrescentFilter", menuName = "LevelGen/Filters/Crescent Filter")]
    public class CrescentFilterConfigBase : FilterConfigBase
    {
        [Header("Crescent Parameters")]
        [Range(0.2f, 1f)] public float outerRadiusScale = 0.4f;
        [Range(0.1f, 0.8f)] public float innerRadiusScale = 0.3f;
        [Range(0.05f, 0.5f)] public float offsetScale = 0.15f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Crescent Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CrescentFilter(outerRadiusScale, innerRadiusScale, offsetScale, fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new CrescentFilter(
                RandomScale(0.3f, 0.7f), // outerRadiusScale
                UnityEngine.Random.Range(0.15f, 0.5f), // innerRadiusScale
                UnityEngine.Random.Range(0.1f, 0.3f), // offsetScale
                RandomBool(0.8f), // fillInside
                RandomCenter().x, // centerX
                RandomCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
