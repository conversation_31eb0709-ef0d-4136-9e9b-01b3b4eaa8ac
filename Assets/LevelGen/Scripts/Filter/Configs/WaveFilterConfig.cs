using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Wave Filter
    /// </summary>
    [CreateAssetMenu(fileName = "WaveFilter", menuName = "LevelGen/Filters/Wave Filter")]
    public class WaveFilterConfig : FilterConfigBase
    {
        [Header("Wave Parameters")]
        [Range(0.5f, 10f)] public float frequency = 2f;
        [Range(0.05f, 0.5f)] public float amplitudeScale = 0.1f;
        [Range(0.01f, 0.2f)] public float thicknessScale = 0.05f;
        public WaveFilter.WaveDirection direction = WaveFilter.WaveDirection.Horizontal;
        [Range(0f, 360f)] public float phase = 0f;
        public WaveFilter.WaveType waveType = WaveFilter.WaveType.Sine;

        public override string GetFilterName()
        {
            return "Wave Filter";
        }

        public override IFilter CreateFilter()
        {
            return new WaveFilter(frequency, amplitudeScale, thicknessScale, direction, phase, waveType);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            var directions = System.Enum.GetValues(typeof(WaveFilter.WaveDirection));
            var waveTypes = System.Enum.GetValues(typeof(WaveFilter.WaveType));
            
            return new WaveFilter(
                UnityEngine.Random.Range(1f, 6f), // frequency
                UnityEngine.Random.Range(0.05f, 0.3f), // amplitudeScale
                UnityEngine.Random.Range(0.02f, 0.1f), // thicknessScale
                (WaveFilter.WaveDirection)directions.GetValue(UnityEngine.Random.Range(0, directions.Length)),
                UnityEngine.Random.Range(0f, 360f), // phase
                (WaveFilter.WaveType)waveTypes.GetValue(UnityEngine.Random.Range(0, waveTypes.Length))
            );
        }
    }
}
