using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Circle Filter
    /// </summary>
    [CreateAssetMenu(fileName = "CircleFilter", menuName = "LevelGen/Filters/Circle Filter")]
    public class CircleFilterConfigBase : FilterConfigBase
    {
        [Header("Circle Parameters")]
        [Range(0.1f, 2f)] public float scaleX = 0.4f;
        [Range(0.1f, 2f)] public float scaleY = 0.4f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;

        public override string GetFilterName()
        {
            return "Circle Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CircleFilter(scaleX, scaleY, fillInside, centerX, centerY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new CircleFilter(
                RandomScale(0.2f, 1.5f), // scaleX
                RandomScale(0.2f, 1.5f), // scaleY
                RandomBool(0.8f)// fillInside (80% chance true)
            );
        }
    }
}
