using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Grid Filter
    /// </summary>
    [CreateAssetMenu(fileName = "GridFilter", menuName = "LevelGen/Filters/Grid Filter")]
    public class GridFilterConfig : FilterConfigBase
    {
        [Header("Grid Parameters")]
        [Range(1, 20)] public int cellWidth = 4;
        [Range(1, 20)] public int cellHeight = 4;
        [Range(1, 5)] public int lineThickness = 1;
        public bool fillCells = false;
        [Range(-10, 10)] public int offsetX = 0;
        [Range(-10, 10)] public int offsetY = 0;

        public override string GetFilterName()
        {
            return "Grid Filter";
        }

        public override IFilter CreateFilter()
        {
            return new GridFilter(cellWidth, lineThickness, !fillCells, offsetX, offsetY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new GridFilter(
                UnityEngine.Random.Range(3, 12), // cellSize
                UnityEngine.Random.Range(1, 3), // lineThickness
                !RandomBool(0.3f), // fillLines (70% chance)
                UnityEngine.Random.Range(-5, 6), // offsetX
                UnityEngine.Random.Range(-5, 6)  // offsetY
            );
        }
    }
}
