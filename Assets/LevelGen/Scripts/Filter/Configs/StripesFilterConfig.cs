using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Stripes Filter
    /// </summary>
    [CreateAssetMenu(fileName = "StripesFilter", menuName = "LevelGen/Filters/Stripes Filter")]
    public class StripesFilterConfig : FilterConfigBase
    {
        [Header("Stripes Parameters")]
        [Range(1, 20)] public int stripeWidth = 4;
        [Range(1, 20)] public int gapWidth = 4;
        [Range(0f, 360f)] public float rotation = 0f;
        public bool fillStripes = true;
        [Range(-20, 20)] public int offsetX = 0;
        [Range(-20, 20)] public int offsetY = 0;

        public override string GetFilterName()
        {
            return "Stripes Filter";
        }

        public override IFilter CreateFilter()
        {
            return new StripesFilter(stripeWidth, gapWidth, rotation, fillStripes, offsetX, offsetY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new StripesFilter(
                UnityEngine.Random.Range(2, 12), // stripeWidth
                UnityEngine.Random.Range(2, 12), // gapWidth
                RandomRotation(), // rotation
                RandomBool(0.7f), // fillStripes (70% chance)
                UnityEngine.Random.Range(-10, 11), // offsetX
                UnityEngine.Random.Range(-10, 11) // offsetY
            );
        }
    }
}
