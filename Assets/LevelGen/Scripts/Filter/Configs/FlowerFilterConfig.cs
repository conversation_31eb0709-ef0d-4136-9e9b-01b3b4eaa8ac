using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Flower Filter
    /// </summary>
    [CreateAssetMenu(fileName = "FlowerFilter", menuName = "LevelGen/Filters/Basic Shapes/Flower Filter")]
    public class FlowerFilterConfigBase : FilterConfigBase
    {
        [Header("Flower Parameters")]
        [Range(3, 12)] public int petalCount = 6;
        [Range(0.1f, 1f)] public float petalSizeScale = 0.3f;
        [Range(0.05f, 0.5f)] public float centerSizeScale = 0.1f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Flower Filter";
        }

        public override IFilter CreateFilter()
        {
            return new FlowerFilter(petalCount, petalSizeScale, centerSizeScale, fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new FlowerFilter(
                UnityEngine.Random.Range(4, 10), // petalCount
                RandomScale(0.2f, 0.6f), // petalSizeScale
                UnityEngine.Random.Range(0.05f, 0.2f), // centerSizeScale
                RandomBool(0.8f), // fillInside
                RandomCenter().x, // centerX
                RandomCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
