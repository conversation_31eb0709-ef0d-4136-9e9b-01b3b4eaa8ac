using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Connectivity Filter
    /// </summary>
    [CreateAssetMenu(fileName = "ConnectivityFilter", menuName = "LevelGen/Filters/Connectivity Filter")]
    public class ConnectivityFilterConfigBase : FilterConfigBase
    {
        [Header("Connectivity Parameters")]
        [Range(1, 50)] public int minComponentSize = 5;
        public bool keepLargestComponent = true;
        public bool connectComponents = false;
        public ConnectivityFilter.ConnectivityType connectivityType = ConnectivityFilter.ConnectivityType.FourConnected;

        public override string GetFilterName()
        {
            return "Connectivity Filter";
        }

        public override IFilter CreateFilter()
        {
            return new ConnectivityFilter(minComponentSize, keepLargestComponent, connectComponents, connectivityType);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            var connectivityTypes = System.Enum.GetValues(typeof(ConnectivityFilter.ConnectivityType));
            
            return new ConnectivityFilter(
                UnityEngine.Random.Range(3, 15), // minComponentSize
                RandomBool(0.6f), // keepLargestComponent (60% chance)
                RandomBool(0.3f), // connectComponents (30% chance)
                (ConnectivityFilter.ConnectivityType)connectivityTypes.GetValue(UnityEngine.Random.Range(0, connectivityTypes.Length))
            );
        }
    }
}
