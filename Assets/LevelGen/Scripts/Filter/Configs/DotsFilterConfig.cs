using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Dots Filter
    /// </summary>
    [CreateAssetMenu(fileName = "DotsFilter", menuName = "LevelGen/Filters/Dots Filter")]
    public class DotsFilterConfig : FilterConfigBase
    {
        [Header("Dots Parameters")]
        [Range(1, 20)] public int spacingX = 6;
        [Range(1, 20)] public int spacingY = 6;
        [Range(1, 10)] public int dotRadius = 2;
        [Range(-10, 10)] public int offsetX = 0;
        [Range(-10, 10)] public int offsetY = 0;
        public bool fillDots = true;

        public override string GetFilterName()
        {
            return "Dots Filter";
        }

        public override IFilter CreateFilter()
        {
            return new DotsFilter(spacingX, dotRadius, fillDots, offsetX, offsetY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new DotsFilter(
                UnityEngine.Random.Range(4, 15), // spacing
                UnityEngine.Random.Range(1, 6), // dotRadius
                RandomBool(0.8f), // fillDots (80% chance)
                UnityEngine.Random.Range(-5, 6), // offsetX
                UnityEngine.Random.Range(-5, 6) // offsetY
            );
        }
    }
}
