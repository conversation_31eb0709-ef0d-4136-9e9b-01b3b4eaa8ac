using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Star Filter
    /// </summary>
    [CreateAssetMenu(fileName = "StarFilter", menuName = "LevelGen/Filters/Star Filter")]
    public class StarFilterConfigBase : FilterConfigBase
    {
        [Header("Star Parameters")]
        [Range(3, 12)] public int points = 5;
        [Range(0.2f, 1f)] public float outerRadiusScale = 0.4f;
        [Range(0.1f, 0.8f)] public float innerRadiusScale = 0.2f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Star Filter";
        }

        public override IFilter CreateFilter()
        {
            return new StarFilter(points, outerRadiusScale, innerRadiusScale, fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new StarFilter(
                UnityEngine.Random.Range(3, 9), // points
                RandomScale(0.3f, 0.8f), // outerRadiusScale
                RandomScale(0.1f, 0.4f), // innerRadiusScale
                RandomBool(0.8f), // fillInside
                RandomCenter().x, // centerX
                RandomCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
