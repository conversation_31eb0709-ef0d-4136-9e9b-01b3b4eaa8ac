using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Ring Filter
    /// </summary>
    [CreateAssetMenu(fileName = "RingFilter", menuName = "LevelGen/Filters/Ring Filter")]
    public class RingFilterConfig : FilterConfigBase
    {
        [Header("Ring Parameters")]
        [Range(0.1f, 1f)] public float outerRadiusScale = 0.4f;
        [Range(0.05f, 0.8f)] public float innerRadiusScale = 0.2f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;

        public override string GetFilterName()
        {
            return "Ring Filter";
        }

        public override IFilter CreateFilter()
        {
            return new RingFilter(outerRadiusScale, innerRadiusScale, fillInside, centerX, centerY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            float outerRadius = UnityEngine.Random.Range(0.2f, 0.8f);
            float innerRadius = UnityEngine.Random.Range(0.1f, outerRadius * 0.8f); // Inner radius should be smaller than outer
            
            return new RingFilter(
                outerRadius, // outerRadiusScale
                innerRadius, // innerRadiusScale
                RandomBool(0.8f), // fillInside (80% chance)
                RandomCenter().x, // centerX
                RandomCenter().y // centerY
            );
        }
    }
}
