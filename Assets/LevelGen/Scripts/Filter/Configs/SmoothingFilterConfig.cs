using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Smoothing Filter
    /// </summary>
    [CreateAssetMenu(fileName = "SmoothingFilter", menuName = "LevelGen/Filters/Smoothing Filter")]
    public class SmoothingFilterConfigBase : FilterConfigBase
    {
        [Header("Smoothing Parameters")]
        public SmoothingFilter.SmoothingType smoothingType = SmoothingFilter.SmoothingType.Gaussian;
        [Range(1, 5)] public int iterations = 1;
        [Range(0f, 1f)] public float threshold = 0.5f;

        public override string GetFilterName()
        {
            return "Smoothing Filter";
        }

        public override IFilter CreateFilter()
        {
            return new SmoothingFilter(smoothingType, iterations, threshold);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            var smoothingTypes = System.Enum.GetValues(typeof(SmoothingFilter.SmoothingType));
            
            return new SmoothingFilter(
                (SmoothingFilter.SmoothingType)smoothingTypes.GetValue(UnityEngine.Random.Range(0, smoothingTypes.Length)),
                UnityEngine.Random.Range(1, 4), // iterations
                UnityEngine.Random.Range(0.3f, 0.7f) // threshold
            );
        }
    }
}
