using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter
{
    /// <summary>
    /// Group để quản lý và tạo các Processing Filter Configs
    /// </summary>
    [CreateAssetMenu(fileName = "ProcessingFiltersConfigGroup", menuName = "LevelGen/Config Groups/Processing Filters Group")]
    public class ProcessingFiltersConfigGroup : ScriptableObject
    {
        [Header("Processing Filters Config Group")]
        [InfoBox("Use buttons below to create individual processing filter configs")]
        
        [But<PERSON>("Create Smoothing Filter Config", ButtonSizes.Large)]
        public void CreateSmoothingConfig()
        {
            CreateConfig<SmoothingFilterConfigBase>("SmoothingFilter");
        }
        
        [But<PERSON>("Create Connectivity Filter Config", ButtonSizes.Large)]
        public void CreateConnectivityConfig()
        {
            CreateConfig<ConnectivityFilterConfigBase>("ConnectivityFilter");
        }
        
        [Space(20)]
        [But<PERSON>("Create All Processing Filter Configs", ButtonSizes.Large)]
        public void CreateAllProcessingFilterConfigs()
        {
            CreateSmoothingConfig();
            CreateConnectivityConfig();
            
            Debug.Log("Created all Processing filter configs!");
        }
        
        private void CreateConfig<T>(string fileName) where T : FilterConfigBase
        {
#if UNITY_EDITOR
            var config = ScriptableObject.CreateInstance<T>();
            config.filterName = fileName;
            
            string path = $"Assets/LevelGen/Scripts/Filter/Configs/ProcessingFilters/{fileName}.asset";
            UnityEditor.AssetDatabase.CreateAsset(config, path);
            UnityEditor.AssetDatabase.SaveAssets();
            
            Debug.Log($"Created {typeof(T).Name} at {path}");
            
            // Ping the created asset
            UnityEditor.EditorGUIUtility.PingObject(config);
#endif
        }
    }
}
