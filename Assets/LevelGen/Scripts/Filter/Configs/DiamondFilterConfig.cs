using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Diamond Filter
    /// </summary>
    [CreateAssetMenu(fileName = "DiamondFilter", menuName = "LevelGen/Filters/Diamond Filter")]
    public class DiamondFilterConfigBase : FilterConfigBase
    {
        [Header("Diamond Parameters")]
        [Range(0.1f, 2f)] public float sizeScale = 0.8f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;

        public override string GetFilterName()
        {
            return "Diamond Filter";
        }

        public override IFilter CreateFilter()
        {
            return new DiamondFilter(sizeScale, fillInside, centerX, centerY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new DiamondFilter(
                RandomScale(0.4f, 1.4f), // sizeScale
                RandomBool(0.8f), // fillInside
                RandomCenter().x, // centerX
                RandomCenter().y  // centerY
            );
        }
    }
}
