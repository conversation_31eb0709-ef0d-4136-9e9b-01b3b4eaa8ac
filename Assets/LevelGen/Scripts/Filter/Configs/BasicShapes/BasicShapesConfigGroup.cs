using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter
{
    /// <summary>
    /// Group để quản lý và tạo các Basic Shape Filter Configs
    /// </summary>
    [CreateAssetMenu(fileName = "BasicShapesConfigGroup", menuName = "LevelGen/Config Groups/Basic Shapes Group")]
    public class BasicShapesConfigGroup : ScriptableObject
    {
        [Header("Basic Shapes Config Group")]
        [InfoBox("Use buttons below to create individual filter configs")]
        
        [<PERSON><PERSON>("Create Circle Filter Config", ButtonSizes.Large)]
        public void CreateCircleConfig()
        {
            CreateConfig<CircleFilterConfig>("CircleFilter");
        }
        
        [But<PERSON>("Create Rectangle Filter Config", ButtonSizes.Large)]
        public void CreateRectangleConfig()
        {
            CreateConfig<RectangleFilterConfig>("RectangleFilter");
        }
        
        [But<PERSON>("Create Diamond Filter Config", ButtonSizes.Large)]
        public void CreateDiamondConfig()
        {
            CreateConfig<DiamondFilterConfig>("DiamondFilter");
        }
        
        [<PERSON><PERSON>("Create Star Filter Config", ButtonSizes.Large)]
        public void CreateStarConfig()
        {
            CreateConfig<StarFilterConfig>("StarFilter");
        }
        
        [Button("Create Heart Filter Config", ButtonSizes.Large)]
        public void CreateHeartConfig()
        {
            CreateConfig<HeartFilterConfig>("HeartFilter");
        }
        
        [Button("Create Flower Filter Config", ButtonSizes.Large)]
        public void CreateFlowerConfig()
        {
            CreateConfig<FlowerFilterConfig>("FlowerFilter");
        }
        
        [Button("Create Cross Filter Config", ButtonSizes.Large)]
        public void CreateCrossConfig()
        {
            CreateConfig<CrossFilterConfig>("CrossFilter");
        }
        
        [Button("Create Polygon Filter Config", ButtonSizes.Large)]
        public void CreatePolygonConfig()
        {
            CreateConfig<PolygonFilterConfig>("PolygonFilter");
        }
        
        [Space(20)]
        [Button("Create All Basic Shape Configs", ButtonSizes.Large)]
        public void CreateAllBasicShapeConfigs()
        {
            CreateCircleConfig();
            CreateRectangleConfig();
            CreateDiamondConfig();
            CreateStarConfig();
            CreateHeartConfig();
            CreateFlowerConfig();
            CreateCrossConfig();
            CreatePolygonConfig();
            
            Debug.Log("Created all Basic Shape filter configs!");
        }
        
        private void CreateConfig<T>(string fileName) where T : FilterConfig
        {
#if UNITY_EDITOR
            var config = ScriptableObject.CreateInstance<T>();
            config.filterName = fileName;
            
            string path = $"Assets/LevelGen/Scripts/Filter/Configs/BasicShapes/{fileName}.asset";
            UnityEditor.AssetDatabase.CreateAsset(config, path);
            UnityEditor.AssetDatabase.SaveAssets();
            
            Debug.Log($"Created {typeof(T).Name} at {path}");
            
            // Ping the created asset
            UnityEditor.EditorGUIUtility.PingObject(config);
#endif
        }
    }
}
