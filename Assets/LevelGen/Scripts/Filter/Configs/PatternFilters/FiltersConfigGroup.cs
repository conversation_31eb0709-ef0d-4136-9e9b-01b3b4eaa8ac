using Sirenix.OdinInspector;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs.PatternFilters
{
    /// <summary>
    /// Group để quản lý và tạo các Pattern Filter Configs
    /// </summary>
    [CreateAssetMenu(fileName = "PatternFiltersConfigGroup", menuName = "LevelGen/Config Groups/Pattern Filters Group")]
    public class PatternFiltersConfigGroup : ScriptableObject
    {
        [Header("Pattern Filters Config Group")]
        [InfoBox("Use buttons below to create individual pattern filter configs")]
        
        [Button("Create Spiral Filter Config", ButtonSizes.Large)]
        public void CreateSpiralConfig()
        {
            CreateConfig<SpiralFilterConfigBase>("SpiralFilter");
        }
        
        [Button("Create Checkerboard Filter Config", ButtonSizes.Large)]
        public void CreateCheckerboardConfig()
        {
            CreateConfig<CheckerboardFilterConfig>("CheckerboardFilter");
        }
        
        [But<PERSON>("Create Maze Filter Config", ButtonSizes.Large)]
        public void CreateMazeConfig()
        {
            CreateConfig<MazeFilterConfig>("MazeFilter");
        }
        
        [Button("Create Wave Filter Config", ButtonSizes.Large)]
        public void CreateWaveConfig()
        {
            CreateConfig<WaveFilterConfig>("WaveFilter");
        }
        
        [Button("Create Grid Filter Config", ButtonSizes.Large)]
        public void CreateGridConfig()
        {
            CreateConfig<GridFilterConfig>("GridFilter");
        }
        
        [Button("Create Dots Filter Config", ButtonSizes.Large)]
        public void CreateDotsConfig()
        {
            CreateConfig<DotsFilterConfig>("DotsFilter");
        }
        
        [Button("Create Stripes Filter Config", ButtonSizes.Large)]
        public void CreateStripesConfig()
        {
            CreateConfig<StripesFilterConfig>("StripesFilter");
        }
        
        [Button("Create Noise Filter Config", ButtonSizes.Large)]
        public void CreateNoiseConfig()
        {
            CreateConfig<NoiseFilterConfigBase>("NoiseFilter");
        }
        
        [Space(20)]
        [Button("Create All Pattern Filter Configs", ButtonSizes.Large)]
        public void CreateAllPatternFilterConfigs()
        {
            CreateSpiralConfig();
            CreateCheckerboardConfig();
            CreateMazeConfig();
            CreateWaveConfig();
            CreateGridConfig();
            CreateDotsConfig();
            CreateStripesConfig();
            CreateNoiseConfig();
            
            Debug.Log("Created all Pattern filter configs!");
        }
        
        private void CreateConfig<T>(string fileName) where T : FilterConfigBase
        {
#if UNITY_EDITOR
            var config = ScriptableObject.CreateInstance<T>();
            config.filterName = fileName;
            
            string path = $"Assets/LevelGen/Scripts/Filter/Configs/PatternFilters/{fileName}.asset";
            UnityEditor.AssetDatabase.CreateAsset(config, path);
            UnityEditor.AssetDatabase.SaveAssets();
            
            Debug.Log($"Created {typeof(T).Name} at {path}");
            
            // Ping the created asset
            UnityEditor.EditorGUIUtility.PingObject(config);
#endif
        }
    }
}
